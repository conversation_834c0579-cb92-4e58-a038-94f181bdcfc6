#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格比率盈亏检查机制
"""

import pandas as pd
import numpy as np
from src.strategy.pairs_strategy import PairsStrategy
from src.engine.engine import BacktestEngine
import os

def test_profit_check_mechanism():
    """测试价格比率盈亏检查机制"""
    print("测试价格比率盈亏检查机制")
    print("=" * 50)
    
    # 查找可用的数据文件
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录 {data_dir} 不存在")
        return
    
    # 查找ETF数据文件
    etf_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    if len(etf_files) < 2:
        print(f"错误: 需要至少2个ETF数据文件，当前只找到 {len(etf_files)} 个")
        return
    
    # 选择前两个文件进行测试
    etf1_file = etf_files[0]
    etf2_file = etf_files[1]
    
    print(f"使用数据文件:")
    print(f"  ETF1: {etf1_file}")
    print(f"  ETF2: {etf2_file}")
    
    # 加载数据
    try:
        etf1_data = pd.read_csv(os.path.join(data_dir, etf1_file))
        etf2_data = pd.read_csv(os.path.join(data_dir, etf2_file))

        # 处理时间列 - 根据实际数据格式
        def process_time_data(df):
            # 合并日期和时间列
            df['datetime'] = pd.to_datetime(df['date'].astype(str) + ' ' + df['time'].astype(str).str.zfill(8),
                                          format='%Y%m%d %H%M%S%f')
            df.set_index('datetime', inplace=True)
            return df

        etf1_data = process_time_data(etf1_data)
        etf2_data = process_time_data(etf2_data)

        # 提取ETF代码
        etf1_symbol = etf1_file.split('-')[2]
        etf2_symbol = etf2_file.split('-')[2]

        # 合并数据 - 使用last价格作为收盘价
        data = pd.DataFrame({
            etf1_symbol: etf1_data['last'] / 10000,  # 转换为正常价格格式
            etf2_symbol: etf2_data['last'] / 10000   # 转换为正常价格格式
        }).dropna()
        
        print(f"\n数据统计:")
        print(f"  时间范围: {data.index[0]} 至 {data.index[-1]}")
        print(f"  数据点数: {len(data)}")
        print(f"  ETF对: {etf1_symbol} - {etf2_symbol}")
        
        # 创建策略实例（启用盈亏检查）
        strategy = PairsStrategy(
            window=20,
            std_dev_mult=1.5,
            max_pos_size=0.8,
            verbose=True,
            output_dir="results"
        )
        
        # 确保盈亏检查启用
        strategy.profit_check_enabled = True
        
        # 创建回测引擎
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=1000000.0,
            commission_rate=0.0003,
            slippage_rate=0.0001,
            verbose=True,
            output_dir="results"
        )
        
        print(f"\n开始回测...")
        print(f"初始资金: {engine.initial_capital:,.0f}")
        print(f"手续费率: {engine.commission_rate:.4f}")
        print(f"滑点率: {engine.slippage_rate:.4f}")
        
        # 运行回测
        results = engine.run()
        
        print(f"\n回测完成!")
        # 从results中获取正确的键名
        if 'final_equity' in results:
            print(f"最终权益: {results['final_equity']:,.2f}")
        if 'total_return' in results:
            print(f"总收益率: {results['total_return']:.2%}")

        # 显示results中的所有键，用于调试
        print(f"Results keys: {list(results.keys())}")
        
        # 保存结果
        strategy.save_orders_to_csv()
        
        # 显示盈亏检查统计
        if strategy.profit_check_log:
            total_checks = len(strategy.profit_check_log)
            allowed_trades = sum(1 for log in strategy.profit_check_log if log['can_trade'])
            skipped_trades = total_checks - allowed_trades
            
            print(f"\n盈亏检查统计:")
            print(f"  总检查次数: {total_checks}")
            print(f"  允许交易: {allowed_trades}")
            print(f"  跳过交易: {skipped_trades}")
            if total_checks > 0:
                print(f"  交易通过率: {allowed_trades/total_checks:.2%}")
        
        return results
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_profit_check_mechanism()
