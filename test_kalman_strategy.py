#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡尔曼滤波器策略测试脚本

快速测试卡尔曼滤波器策略的基本功能
"""

import sys
import os
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加src目录到Python路径
sys.path.append('src')

def create_test_data(n_points=200):
    """创建测试数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=n_points, freq='D')
    
    # 生成相关的ETF价格
    returns1 = np.random.normal(0.001, 0.02, n_points)
    returns2 = 0.8 * returns1 + 0.6 * np.random.normal(0.0008, 0.015, n_points)
    
    price1 = 100 * np.exp(np.cumsum(returns1))
    price2 = 95 * np.exp(np.cumsum(returns2))
    
    return pd.DataFrame({
        'ETF1': price1,
        'ETF2': price2
    }, index=dates)

def test_kalman_filter():
    """测试卡尔曼滤波器基本功能"""
    print("测试卡尔曼滤波器基本功能...")
    
    from strategy.kalman_filter import KalmanFilter
    
    # 创建滤波器
    kf = KalmanFilter(
        process_noise_ratio=1e-4,
        measurement_noise_ratio=1e-2
    )
    
    # 测试数据
    observations = [1.0, 1.1, 0.9, 1.2, 0.8, 1.3, 0.7, 1.4]
    
    print("观测序列:", observations)
    print("\n滤波结果:")
    print("观测值\t滤波值\t不确定性\t速度")
    print("-" * 50)
    
    for i, obs in enumerate(observations):
        result = kf.update(obs)
        print(f"{obs:.3f}\t{result['price_ratio']:.3f}\t{result['uncertainty']:.3f}\t{result['velocity']:.3f}")
    
    # 测试交易信号
    print("\n交易信号测试:")
    for threshold in [1.0, 1.5, 2.0]:
        signal = kf.get_mean_reversion_signal(1.5, threshold)
        print(f"阈值{threshold}: 信号={signal}")
    
    print("✓ 卡尔曼滤波器测试通过")

def test_kalman_strategy():
    """测试卡尔曼滤波器策略"""
    print("\n测试卡尔曼滤波器策略...")
    
    from strategy.kalman_pairs_strategy import KalmanPairsStrategy
    
    # 创建测试数据
    data = create_test_data(100)
    print(f"测试数据形状: {data.shape}")
    
    # 创建策略
    strategy = KalmanPairsStrategy(
        process_noise_ratio=1e-4,
        measurement_noise_ratio=1e-2,
        trading_threshold=2.0,
        verbose=True
    )
    
    # 模拟引擎
    class MockEngine:
        def __init__(self, data):
            self.data = data
            self.commission_rate = 0.0003
            self.slippage_rate = 0.0001
            self.portfolio = {'positions': {}, 'cash': 1000000}
    
    engine = MockEngine(data)
    
    # 初始化策略
    strategy.initialize(engine)
    
    # 检查信号计算
    print(f"信号数据形状: {strategy.signals.shape}")
    print(f"信号列: {list(strategy.signals.columns)}")
    
    # 检查交易信号数量
    signal_counts = strategy.signals['trade_signal'].value_counts()
    print(f"交易信号统计: {signal_counts.to_dict()}")
    
    # 测试单个时间点的信号
    test_timestamp = data.index[50]
    signal_info = strategy.get_signal_at_time(test_timestamp)
    print(f"\n时间点 {test_timestamp} 的信号信息:")
    for key, value in signal_info.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # 测试性能摘要
    perf_summary = strategy.get_performance_summary()
    print(f"\n性能摘要:")
    print(f"  总更新次数: {perf_summary['total_updates']}")
    print(f"  平均计算时间: {perf_summary.get('avg_computation_time', 0):.4f}秒")
    
    print("✓ 卡尔曼滤波器策略测试通过")

def test_signal_generator():
    """测试信号生成器"""
    print("\n测试信号生成器...")
    
    from strategy.kalman_filter import KalmanFilter
    from strategy.kalman_signal_generator import KalmanSignalGenerator, SignalType, SignalStrength
    
    # 创建卡尔曼滤波器
    kf = KalmanFilter()
    
    # 创建信号生成器
    signal_gen = KalmanSignalGenerator(kf)
    
    # 测试不同类型的信号
    test_cases = [
        (1.0, 1.0, 0.1),  # 无偏离
        (1.0, 0.5, 0.1),  # 正偏离
        (1.0, 1.5, 0.1),  # 负偏离
        (1.0, 0.2, 0.1),  # 强正偏离
        (1.0, 1.8, 0.1),  # 强负偏离
    ]
    
    print("价格比率\t滤波值\t不确定性\t信号类型\t信号强度")
    print("-" * 60)
    
    for price_ratio, filtered_ratio, uncertainty in test_cases:
        signal_type, signal_strength, info = signal_gen.generate_mean_reversion_signal(
            price_ratio, filtered_ratio, uncertainty
        )
        print(f"{price_ratio:.1f}\t\t{filtered_ratio:.1f}\t{uncertainty:.1f}\t\t{signal_type.name}\t{signal_strength.name}")
    
    print("✓ 信号生成器测试通过")

def test_performance_benchmark():
    """测试性能基准"""
    print("\n测试性能基准...")
    
    from strategy.performance_benchmark import PerformanceBenchmark
    
    # 创建小规模测试数据
    data = create_test_data(50)
    
    benchmark = PerformanceBenchmark(data, verbose=False)
    
    # 只测试计算时间
    results = benchmark.benchmark_computation_time(
        rolling_windows=[10, 20],
        kalman_params={'process_noise_ratio': 1e-4, 'measurement_noise_ratio': 1e-2, 'trading_threshold': 2.0}
    )
    
    print("计算时间对比:")
    kalman_time = results['kalman_filter']['computation_time']
    print(f"  卡尔曼滤波器: {kalman_time:.4f}秒")
    
    for window in [10, 20]:
        rolling_time = results['rolling_regression'][window]['computation_time']
        speedup = results['performance_improvement'][window]['speedup_factor']
        print(f"  滚动回归(窗口{window}): {rolling_time:.4f}秒 (慢{speedup:.1f}倍)")
    
    print("✓ 性能基准测试通过")

def test_visualization():
    """测试可视化功能"""
    print("\n测试可视化功能...")
    
    try:
        from analysis.kalman_visualizer import KalmanVisualizer
        from strategy.kalman_pairs_strategy import KalmanPairsStrategy
        
        # 创建测试数据和策略
        data = create_test_data(100)
        strategy = KalmanPairsStrategy(verbose=False)
        
        class MockEngine:
            def __init__(self, data):
                self.data = data
                self.commission_rate = 0.0003
                self.slippage_rate = 0.0001
        
        engine = MockEngine(data)
        strategy.initialize(engine)
        
        # 创建可视化工具
        visualizer = KalmanVisualizer(output_dir="test_results")
        
        # 测试基本绘图功能
        visualizer.plot_price_ratio_and_filter(
            strategy.signals,
            save_name="test_price_ratio.png"
        )
        
        visualizer.plot_uncertainty_evolution(
            strategy.signals,
            save_name="test_uncertainty.png"
        )
        
        print("✓ 可视化功能测试通过")
        print("  测试图表已保存到 test_results/ 目录")
        
    except ImportError as e:
        print(f"⚠ 可视化测试跳过 (缺少依赖): {e}")
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")

def main():
    """主测试函数"""
    print("卡尔曼滤波器策略测试")
    print("=" * 50)
    
    # 创建测试结果目录
    os.makedirs("test_results", exist_ok=True)
    
    try:
        # 运行各项测试
        test_kalman_filter()
        test_kalman_strategy()
        test_signal_generator()
        test_performance_benchmark()
        test_visualization()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！")
        print("卡尔曼滤波器策略实现正常工作")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
