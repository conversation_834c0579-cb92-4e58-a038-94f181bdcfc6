#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡尔曼滤波器配对交易策略演示脚本

本脚本展示如何使用卡尔曼滤波器进行ETF配对交易，并与传统滚动回归策略进行对比。

主要功能：
1. 加载ETF数据
2. 运行卡尔曼滤波器策略
3. 运行传统滚动回归策略
4. 性能基准测试
5. 结果可视化
6. 生成分析报告

作者: AI Assistant
日期: 2025-01-14
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加src目录到Python路径
sys.path.append('src')

def load_sample_data():
    """
    加载示例数据
    
    Returns:
        pd.DataFrame: ETF价格数据
    """
    print("正在加载数据...")
    
    # 尝试加载真实数据
    data_files = ['data/etf_data.csv', 'data/sample_data.csv']
    
    for file_path in data_files:
        if os.path.exists(file_path):
            try:
                data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                print(f"成功加载数据文件: {file_path}")
                print(f"数据形状: {data.shape}")
                print(f"数据列: {list(data.columns)}")
                print(f"数据期间: {data.index[0]} 到 {data.index[-1]}")
                return data
            except Exception as e:
                print(f"加载数据文件 {file_path} 失败: {e}")
                continue
    
    # 如果没有真实数据，生成模拟数据
    print("未找到真实数据，生成模拟数据...")
    return generate_sample_data()

def generate_sample_data(n_days=500):
    """
    生成模拟ETF数据
    
    Args:
        n_days: 数据天数
        
    Returns:
        pd.DataFrame: 模拟ETF价格数据
    """
    np.random.seed(42)
    
    # 生成日期索引
    dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
    
    # 生成相关的ETF价格
    # ETF1: 基础价格走势
    returns1 = np.random.normal(0.0005, 0.02, n_days)  # 日收益率
    price1 = 100 * np.exp(np.cumsum(returns1))
    
    # ETF2: 与ETF1相关但有一定独立性
    correlation = 0.7
    returns2_independent = np.random.normal(0.0003, 0.018, n_days)
    returns2 = correlation * returns1 + np.sqrt(1 - correlation**2) * returns2_independent
    price2 = 95 * np.exp(np.cumsum(returns2))
    
    # 创建DataFrame
    data = pd.DataFrame({
        'ETF1': price1,
        'ETF2': price2
    }, index=dates)
    
    print(f"生成模拟数据: {data.shape}")
    print(f"ETF1价格范围: {data['ETF1'].min():.2f} - {data['ETF1'].max():.2f}")
    print(f"ETF2价格范围: {data['ETF2'].min():.2f} - {data['ETF2'].max():.2f}")
    
    return data

def run_kalman_strategy(data):
    """
    运行卡尔曼滤波器策略
    
    Args:
        data: ETF价格数据
        
    Returns:
        tuple: (策略实例, 回测结果)
    """
    print("\n" + "="*60)
    print("运行卡尔曼滤波器策略")
    print("="*60)
    
    from strategy.kalman_pairs_strategy import KalmanPairsStrategy
    from engine.engine import BacktestEngine
    
    # 初始化卡尔曼滤波器策略
    kalman_strategy = KalmanPairsStrategy(
        process_noise_ratio=1e-4,
        measurement_noise_ratio=1e-2,
        trading_threshold=2.0,
        max_pos_size=1.0,
        verbose=True,
        output_dir="results/kalman"
    )
    
    # 初始化回测引擎
    engine = BacktestEngine(
        strategy=kalman_strategy,
        data=data,
        initial_capital=1000000.0,
        commission_rate=0.0003,
        slippage_rate=0.0001,
        verbose=True,
        output_dir="results/kalman"
    )
    
    # 运行回测
    print("开始卡尔曼滤波器回测...")
    start_time = datetime.now()
    results = engine.run()
    end_time = datetime.now()
    
    print(f"卡尔曼滤波器回测完成，耗时: {end_time - start_time}")
    
    # 保存结果
    kalman_strategy.save_signals_to_csv()
    kalman_strategy.save_orders_to_csv()
    
    return kalman_strategy, results

def run_rolling_strategy(data):
    """
    运行传统滚动回归策略
    
    Args:
        data: ETF价格数据
        
    Returns:
        tuple: (策略实例, 回测结果)
    """
    print("\n" + "="*60)
    print("运行传统滚动回归策略")
    print("="*60)
    
    from strategy.pairs_strategy import PairsStrategy
    from engine.engine import BacktestEngine
    
    # 初始化滚动回归策略
    rolling_strategy = PairsStrategy(
        window=20,
        std_dev_mult=1.2,
        max_pos_size=1.0,
        verbose=True,
        output_dir="results/rolling"
    )
    
    # 初始化回测引擎
    engine = BacktestEngine(
        strategy=rolling_strategy,
        data=data,
        initial_capital=1000000.0,
        commission_rate=0.0003,
        slippage_rate=0.0001,
        verbose=True,
        output_dir="results/rolling"
    )
    
    # 运行回测
    print("开始滚动回归回测...")
    start_time = datetime.now()
    results = engine.run()
    end_time = datetime.now()
    
    print(f"滚动回归回测完成，耗时: {end_time - start_time}")
    
    return rolling_strategy, results

def run_performance_benchmark(data):
    """
    运行性能基准测试
    
    Args:
        data: ETF价格数据
        
    Returns:
        Dict: 基准测试结果
    """
    print("\n" + "="*60)
    print("运行性能基准测试")
    print("="*60)
    
    from strategy.performance_benchmark import PerformanceBenchmark
    
    benchmark = PerformanceBenchmark(data, verbose=True)
    
    # 计算时间基准测试
    print("1. 计算时间基准测试...")
    computation_results = benchmark.benchmark_computation_time(
        rolling_windows=[10, 20, 40, 60],
        kalman_params={
            'process_noise_ratio': 1e-4,
            'measurement_noise_ratio': 1e-2,
            'trading_threshold': 2.0
        }
    )
    
    # 内存使用基准测试
    print("\n2. 内存使用基准测试...")
    memory_results = benchmark.benchmark_memory_usage()
    
    # 实时处理基准测试
    print("\n3. 实时处理能力基准测试...")
    realtime_results = benchmark.benchmark_real_time_processing(
        batch_sizes=[1, 10, 100]
    )
    
    # 生成性能报告
    benchmark.generate_performance_report("results/performance_report.txt")
    
    return benchmark.results

def create_visualizations(kalman_strategy, rolling_strategy, benchmark_results):
    """
    创建可视化图表
    
    Args:
        kalman_strategy: 卡尔曼滤波器策略实例
        rolling_strategy: 滚动回归策略实例
        benchmark_results: 基准测试结果
    """
    print("\n" + "="*60)
    print("创建可视化图表")
    print("="*60)
    
    from analysis.kalman_visualizer import KalmanVisualizer
    
    visualizer = KalmanVisualizer(output_dir="results/visualizations")
    
    # 1. 卡尔曼滤波器结果可视化
    print("1. 绘制卡尔曼滤波器结果...")
    visualizer.plot_price_ratio_and_filter(
        kalman_strategy.signals,
        title="卡尔曼滤波器价格比率分析",
        save_name="kalman_price_ratio.png"
    )
    
    # 2. 不确定性演化
    print("2. 绘制不确定性演化...")
    visualizer.plot_uncertainty_evolution(
        kalman_strategy.signals,
        save_name="kalman_uncertainty.png"
    )
    
    # 3. 创新序列分析
    print("3. 绘制创新序列分析...")
    visualizer.plot_innovation_analysis(
        kalman_strategy.kalman_filter,
        save_name="kalman_innovation.png"
    )
    
    # 4. 滤波器状态演化
    print("4. 绘制滤波器状态演化...")
    visualizer.plot_filter_state_evolution(
        kalman_strategy.kalman_filter,
        save_name="kalman_state_evolution.png"
    )
    
    # 5. 性能对比
    print("5. 绘制性能对比...")
    visualizer.plot_performance_comparison(
        benchmark_results,
        save_name="performance_comparison.png"
    )
    
    # 6. 综合仪表板
    print("6. 创建综合仪表板...")
    visualizer.create_comprehensive_dashboard(
        kalman_strategy.signals,
        kalman_strategy.kalman_filter,
        benchmark_results,
        save_name="kalman_dashboard.png"
    )

def generate_summary_report(kalman_strategy, rolling_strategy, benchmark_results):
    """
    生成总结报告
    
    Args:
        kalman_strategy: 卡尔曼滤波器策略实例
        rolling_strategy: 滚动回归策略实例
        benchmark_results: 基准测试结果
    """
    print("\n" + "="*60)
    print("生成总结报告")
    print("="*60)
    
    report = []
    report.append("卡尔曼滤波器配对交易策略演示报告")
    report.append("=" * 80)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 策略对比
    report.append("1. 策略对比总结")
    report.append("-" * 40)
    
    # 卡尔曼滤波器性能
    kalman_perf = kalman_strategy.get_performance_summary()
    report.append(f"卡尔曼滤波器策略:")
    report.append(f"  - 总更新次数: {kalman_perf['total_updates']}")
    report.append(f"  - 总交易次数: {kalman_perf['total_trades']}")
    report.append(f"  - 平均计算时间: {kalman_perf['avg_computation_time']:.4f}秒")
    report.append("")
    
    # 性能优势
    if 'computation_time' in benchmark_results:
        ct_results = benchmark_results['computation_time']
        kalman_time = ct_results['kalman_filter']['computation_time']
        
        report.append("2. 性能优势")
        report.append("-" * 40)
        report.append(f"卡尔曼滤波器计算时间: {kalman_time:.4f}秒")
        
        for window in sorted(ct_results['rolling_regression'].keys()):
            rolling_time = ct_results['rolling_regression'][window]['computation_time']
            speedup = ct_results['performance_improvement'][window]['speedup_factor']
            report.append(f"滚动回归(窗口{window}): {rolling_time:.4f}秒 (慢{speedup:.1f}倍)")
        
        report.append("")
    
    # 技术优势
    report.append("3. 技术优势")
    report.append("-" * 40)
    report.append("卡尔曼滤波器相比滚动回归的优势:")
    report.append("• 计算复杂度: O(n) vs O(n*w)")
    report.append("• 内存使用: 常数级 vs 线性增长")
    report.append("• 实时性: 优秀，适合高频交易")
    report.append("• 自适应性: 动态调整模型参数")
    report.append("• 不确定性估计: 提供置信区间")
    report.append("• 数据利用: 利用所有历史信息")
    report.append("")
    
    # 保存报告
    with open("results/summary_report.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(report))
    
    print("总结报告已保存到: results/summary_report.txt")
    print("\n".join(report))

def main():
    """
    主函数
    """
    print("卡尔曼滤波器配对交易策略演示")
    print("=" * 80)
    
    # 创建结果目录
    os.makedirs("results", exist_ok=True)
    os.makedirs("results/kalman", exist_ok=True)
    os.makedirs("results/rolling", exist_ok=True)
    os.makedirs("results/visualizations", exist_ok=True)
    
    try:
        # 1. 加载数据
        data = load_sample_data()
        if data is None or len(data) < 100:
            print("数据加载失败或数据量不足")
            return
        
        # 2. 运行卡尔曼滤波器策略
        kalman_strategy, kalman_results = run_kalman_strategy(data)
        
        # 3. 运行传统滚动回归策略
        rolling_strategy, rolling_results = run_rolling_strategy(data)
        
        # 4. 性能基准测试
        benchmark_results = run_performance_benchmark(data)
        
        # 5. 创建可视化
        create_visualizations(kalman_strategy, rolling_strategy, benchmark_results)
        
        # 6. 生成总结报告
        generate_summary_report(kalman_strategy, rolling_strategy, benchmark_results)
        
        print("\n" + "="*80)
        print("演示完成！")
        print("="*80)
        print("结果文件保存在以下位置:")
        print("• 卡尔曼滤波器结果: results/kalman/")
        print("• 滚动回归结果: results/rolling/")
        print("• 可视化图表: results/visualizations/")
        print("• 性能报告: results/performance_report.txt")
        print("• 总结报告: results/summary_report.txt")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
