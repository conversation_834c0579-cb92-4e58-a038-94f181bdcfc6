#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡尔曼滤波器配对交易策略演示脚本

本脚本展示如何使用卡尔曼滤波器进行ETF配对交易，并与传统滚动回归策略进行对比。

主要功能：
1. 加载ETF数据
2. 运行卡尔曼滤波器策略
3. 运行传统滚动回归策略
4. 性能基准测试
5. 结果可视化
6. 生成分析报告

作者: AI Assistant
日期: 2025-01-14
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加src目录到Python路径
sys.path.append('src')

# 回测参数配置（参考main.py）
BACKTEST_PARAMS = {
    'start_date': '2023-11-27',
    'end_date': '2024-11-25',
    'initial_capital': 100000,
    'data_frequency': 'minute',  # 'day', 'minute', 'tick'
    'commission_rate': 0.0001,
    'slippage_rate': 0.0001
}

def load_real_data():
    """
    使用DataLoader加载真实数据（参考main.py的方式）

    Returns:
        pd.DataFrame: ETF价格数据
    """
    print("正在使用DataLoader加载真实数据...")

    try:
        from src.data.loader import DataLoader

        # 初始化数据加载器
        data_loader = DataLoader()

        # 检查并获取数据
        print("检查本地数据文件...")
        data_updated = data_loader.check_and_fetch_data(
            start_date=BACKTEST_PARAMS['start_date'],
            end_date=BACKTEST_PARAMS['end_date']
        )

        if data_updated:
            print("数据已更新")

        # 加载回测数据
        print("加载回测数据...")
        data = data_loader.load_data(
            start_date=BACKTEST_PARAMS['start_date'],
            end_date=BACKTEST_PARAMS['end_date'],
            frequency=BACKTEST_PARAMS['data_frequency']
        )

        print(f"成功加载真实数据")
        print(f"数据形状: {data.shape}")
        print(f"数据列: {list(data.columns)}")
        print(f"数据期间: {data.index[0]} 到 {data.index[-1]}")
        print(f"数据频率: {BACKTEST_PARAMS['data_frequency']}")

        return data

    except Exception as e:
        print(f"加载真实数据失败: {e}")
        print("将使用模拟数据进行演示...")
        return None

def load_sample_data():
    """
    加载数据（优先使用真实数据，失败时使用模拟数据）

    Returns:
        pd.DataFrame: ETF价格数据
    """
    # 首先尝试加载真实数据
    real_data = load_real_data()
    if real_data is not None and len(real_data) > 100:
        return real_data

    # 如果真实数据加载失败或数据量不足，使用模拟数据
    print("使用模拟数据进行演示...")
    return generate_sample_data()

def generate_sample_data(n_days=500):
    """
    生成模拟ETF数据
    
    Args:
        n_days: 数据天数
        
    Returns:
        pd.DataFrame: 模拟ETF价格数据
    """
    np.random.seed(42)
    
    # 生成日期索引
    dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
    
    # 生成相关的ETF价格
    # ETF1: 基础价格走势
    returns1 = np.random.normal(0.0005, 0.02, n_days)  # 日收益率
    price1 = 100 * np.exp(np.cumsum(returns1))
    
    # ETF2: 与ETF1相关但有一定独立性
    correlation = 0.7
    returns2_independent = np.random.normal(0.0003, 0.018, n_days)
    returns2 = correlation * returns1 + np.sqrt(1 - correlation**2) * returns2_independent
    price2 = 95 * np.exp(np.cumsum(returns2))
    
    # 创建DataFrame
    data = pd.DataFrame({
        'ETF1': price1,
        'ETF2': price2
    }, index=dates)
    
    print(f"生成模拟数据: {data.shape}")
    print(f"ETF1价格范围: {data['ETF1'].min():.2f} - {data['ETF1'].max():.2f}")
    print(f"ETF2价格范围: {data['ETF2'].min():.2f} - {data['ETF2'].max():.2f}")
    
    return data

def run_kalman_strategy(data):
    """
    运行卡尔曼滤波器策略
    
    Args:
        data: ETF价格数据
        
    Returns:
        tuple: (策略实例, 回测结果)
    """
    print("\n" + "="*60)
    print("运行卡尔曼滤波器策略")
    print("="*60)
    
    from src.strategy.kalman_pairs_strategy import KalmanPairsStrategy
    from src.engine.engine import BacktestEngine

    # 初始化卡尔曼滤波器策略
    kalman_strategy = KalmanPairsStrategy(
        process_noise_ratio=1e-4,
        measurement_noise_ratio=1e-2,
        trading_threshold=1.5,  # 降低交易阈值以生成更多信号
        max_pos_size=1.0,
        verbose=True,
        output_dir="results/kalman"
    )

    # 初始化回测引擎
    engine = BacktestEngine(
        strategy=kalman_strategy,
        data=data,
        initial_capital=BACKTEST_PARAMS['initial_capital'],
        commission_rate=BACKTEST_PARAMS['commission_rate'],
        slippage_rate=BACKTEST_PARAMS['slippage_rate'],
        verbose=True,
        output_dir="results/kalman"
    )
    
    # 运行回测
    print("开始卡尔曼滤波器回测...")
    start_time = datetime.now()
    results = engine.run()
    end_time = datetime.now()
    
    print(f"卡尔曼滤波器回测完成，耗时: {end_time - start_time}")
    
    # 保存结果
    kalman_strategy.save_signals_to_csv()
    kalman_strategy.save_orders_to_csv()
    
    return kalman_strategy, results

def run_rolling_strategy(data):
    """
    运行传统滚动回归策略
    
    Args:
        data: ETF价格数据
        
    Returns:
        tuple: (策略实例, 回测结果)
    """
    print("\n" + "="*60)
    print("运行传统滚动回归策略")
    print("="*60)
    
    from src.strategy.pairs_strategy import PairsStrategy
    from src.engine.engine import BacktestEngine

    # 初始化滚动回归策略
    rolling_strategy = PairsStrategy(
        window=20,
        std_dev_mult=1.2,
        max_pos_size=1.0,
        verbose=True,
        output_dir="results/rolling"
    )

    # 初始化回测引擎
    engine = BacktestEngine(
        strategy=rolling_strategy,
        data=data,
        initial_capital=BACKTEST_PARAMS['initial_capital'],
        commission_rate=BACKTEST_PARAMS['commission_rate'],
        slippage_rate=BACKTEST_PARAMS['slippage_rate'],
        verbose=True,
        output_dir="results/rolling"
    )
    
    # 运行回测
    print("开始滚动回归回测...")
    start_time = datetime.now()
    results = engine.run()
    end_time = datetime.now()
    
    print(f"滚动回归回测完成，耗时: {end_time - start_time}")
    
    return rolling_strategy, results

def run_performance_benchmark(data):
    """
    运行性能基准测试
    
    Args:
        data: ETF价格数据
        
    Returns:
        Dict: 基准测试结果
    """
    print("\n" + "="*60)
    print("运行性能基准测试")
    print("="*60)
    
    from src.strategy.performance_benchmark import PerformanceBenchmark
    
    benchmark = PerformanceBenchmark(data, verbose=True)
    
    # 计算时间基准测试
    print("1. 计算时间基准测试...")
    computation_results = benchmark.benchmark_computation_time(
        rolling_windows=[10, 20, 40, 60],
        kalman_params={
            'process_noise_ratio': 1e-4,
            'measurement_noise_ratio': 1e-2,
            'trading_threshold': 2.0
        }
    )
    
    # 内存使用基准测试
    print("\n2. 内存使用基准测试...")
    memory_results = benchmark.benchmark_memory_usage()
    
    # 实时处理基准测试
    print("\n3. 实时处理能力基准测试...")
    realtime_results = benchmark.benchmark_real_time_processing(
        batch_sizes=[1, 10, 100]
    )
    
    # 生成性能报告
    benchmark.generate_performance_report("results/performance_report.txt")
    
    return benchmark.results

def analyze_backtest_results(results, strategy_name="策略"):
    """
    分析回测结果（参考main.py的方式）

    Args:
        results: 回测结果
        strategy_name: 策略名称

    Returns:
        性能指标DataFrame
    """
    try:
        from src.analysis.performance import PerformanceAnalyzer
        from src.analysis.visualizer import Visualizer

        print(f"\n分析{strategy_name}回测结果...")

        # 分析结果
        analyzer = PerformanceAnalyzer(results, backtest_params=BACKTEST_PARAMS)
        performance_metrics = analyzer.analyze()

        # 可视化结果
        visualizer = Visualizer(results)
        current_date = datetime.now().strftime('%Y%m%d')

        # 保存结果
        strategy_dir = f'results/{strategy_name.lower()}'
        os.makedirs(strategy_dir, exist_ok=True)

        visualizer.plot_results(save_dir=strategy_dir)
        performance_metrics.to_csv(
            os.path.join(strategy_dir, f'backtest_report_{current_date}.csv')
        )

        print(f"\n{strategy_name}性能指标:")
        print(performance_metrics)

        return performance_metrics

    except Exception as e:
        print(f"分析{strategy_name}结果时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_visualizations(kalman_strategy, rolling_strategy, benchmark_results):
    """
    创建可视化图表
    
    Args:
        kalman_strategy: 卡尔曼滤波器策略实例
        rolling_strategy: 滚动回归策略实例
        benchmark_results: 基准测试结果
    """
    print("\n" + "="*60)
    print("创建可视化图表")
    print("="*60)
    
    from src.analysis.kalman_visualizer import KalmanVisualizer
    
    visualizer = KalmanVisualizer(output_dir="results/visualizations")
    
    # 1. 卡尔曼滤波器结果可视化
    print("1. 绘制卡尔曼滤波器结果...")
    visualizer.plot_price_ratio_and_filter(
        kalman_strategy.signals,
        title="卡尔曼滤波器价格比率分析",
        save_name="kalman_price_ratio.png"
    )
    
    # 2. 不确定性演化
    print("2. 绘制不确定性演化...")
    visualizer.plot_uncertainty_evolution(
        kalman_strategy.signals,
        save_name="kalman_uncertainty.png"
    )
    
    # 3. 创新序列分析
    print("3. 绘制创新序列分析...")
    visualizer.plot_innovation_analysis(
        kalman_strategy.kalman_filter,
        save_name="kalman_innovation.png"
    )
    
    # 4. 滤波器状态演化
    print("4. 绘制滤波器状态演化...")
    visualizer.plot_filter_state_evolution(
        kalman_strategy.kalman_filter,
        save_name="kalman_state_evolution.png"
    )
    
    # 5. 性能对比
    print("5. 绘制性能对比...")
    visualizer.plot_performance_comparison(
        benchmark_results,
        save_name="performance_comparison.png"
    )
    
    # 6. 综合仪表板
    print("6. 创建综合仪表板...")
    visualizer.create_comprehensive_dashboard(
        kalman_strategy.signals,
        kalman_strategy.kalman_filter,
        benchmark_results,
        save_name="kalman_dashboard.png"
    )

def generate_summary_report(kalman_strategy, rolling_strategy, benchmark_results):
    """
    生成总结报告
    
    Args:
        kalman_strategy: 卡尔曼滤波器策略实例
        rolling_strategy: 滚动回归策略实例
        benchmark_results: 基准测试结果
    """
    print("\n" + "="*60)
    print("生成总结报告")
    print("="*60)
    
    report = []
    report.append("卡尔曼滤波器配对交易策略演示报告")
    report.append("=" * 80)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 策略对比
    report.append("1. 策略对比总结")
    report.append("-" * 40)
    
    # 卡尔曼滤波器性能
    kalman_perf = kalman_strategy.get_performance_summary()
    report.append(f"卡尔曼滤波器策略:")
    report.append(f"  - 总更新次数: {kalman_perf['total_updates']}")
    report.append(f"  - 总交易次数: {kalman_perf['total_trades']}")
    report.append(f"  - 平均计算时间: {kalman_perf['avg_computation_time']:.4f}秒")
    report.append("")
    
    # 性能优势
    if 'computation_time' in benchmark_results:
        ct_results = benchmark_results['computation_time']
        kalman_time = ct_results['kalman_filter']['computation_time']
        
        report.append("2. 性能优势")
        report.append("-" * 40)
        report.append(f"卡尔曼滤波器计算时间: {kalman_time:.4f}秒")
        
        for window in sorted(ct_results['rolling_regression'].keys()):
            rolling_time = ct_results['rolling_regression'][window]['computation_time']
            speedup = ct_results['performance_improvement'][window]['speedup_factor']
            report.append(f"滚动回归(窗口{window}): {rolling_time:.4f}秒 (慢{speedup:.1f}倍)")
        
        report.append("")
    
    # 技术优势
    report.append("3. 技术优势")
    report.append("-" * 40)
    report.append("卡尔曼滤波器相比滚动回归的优势:")
    report.append("• 计算复杂度: O(n) vs O(n*w)")
    report.append("• 内存使用: 常数级 vs 线性增长")
    report.append("• 实时性: 优秀，适合高频交易")
    report.append("• 自适应性: 动态调整模型参数")
    report.append("• 不确定性估计: 提供置信区间")
    report.append("• 数据利用: 利用所有历史信息")
    report.append("")
    
    # 保存报告
    with open("results/summary_report.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(report))
    
    print("总结报告已保存到: results/summary_report.txt")
    print("\n".join(report))

def main():
    """
    主函数
    """
    print("卡尔曼滤波器配对交易策略演示")
    print("=" * 80)
    
    # 创建结果目录
    os.makedirs("results", exist_ok=True)
    os.makedirs("results/kalman", exist_ok=True)
    os.makedirs("results/rolling", exist_ok=True)
    os.makedirs("results/visualizations", exist_ok=True)
    
    try:
        # 1. 加载数据
        data = load_sample_data()
        if data is None or len(data) < 100:
            print("数据加载失败或数据量不足")
            return
        
        # 2. 运行卡尔曼滤波器策略
        kalman_strategy, kalman_results = run_kalman_strategy(data)

        # 3. 分析卡尔曼滤波器回测结果
        kalman_performance = analyze_backtest_results(kalman_results, "卡尔曼滤波器")

        # 4. 运行传统滚动回归策略
        rolling_strategy, rolling_results = run_rolling_strategy(data)

        # 5. 分析滚动回归回测结果
        rolling_performance = analyze_backtest_results(rolling_results, "滚动回归")

        # 6. 性能基准测试
        benchmark_results = run_performance_benchmark(data)

        # 7. 创建卡尔曼滤波器专门的可视化
        create_visualizations(kalman_strategy, rolling_strategy, benchmark_results)

        # 8. 生成总结报告
        generate_summary_report(kalman_strategy, rolling_strategy, benchmark_results)

        # 9. 对比两种策略的性能
        if kalman_performance is not None and rolling_performance is not None:
            print("\n" + "="*80)
            print("策略性能对比")
            print("="*80)

            # 创建对比表
            comparison_df = pd.DataFrame({
                '卡尔曼滤波器': kalman_performance['指标值'],
                '滚动回归': rolling_performance['指标值']
            })

            print(comparison_df)

            # 保存对比结果
            comparison_df.to_csv('results/strategy_comparison.csv', encoding='utf-8')
            print("\n策略对比结果已保存到: results/strategy_comparison.csv")
        
        print("\n" + "="*80)
        print("卡尔曼滤波器配对交易策略演示完成！")
        print("="*80)
        print("结果文件保存在以下位置:")
        print("• 卡尔曼滤波器回测结果: results/卡尔曼滤波器/")
        print("• 滚动回归回测结果: results/滚动回归/")
        print("• 卡尔曼滤波器专用可视化: results/visualizations/")
        print("• 性能基准测试报告: results/performance_report.txt")
        print("• 总结报告: results/summary_report.txt")
        print("• 策略对比结果: results/strategy_comparison.csv")

        print("\n您可以通过以下方式查看更多详细结果:")
        print("1. 查看回测报告CSV文件了解详细性能指标")
        print("2. 查看可视化目录中的图表了解策略表现")
        print("3. 查看性能报告了解卡尔曼滤波器的性能优势")
        print("4. 查看策略对比结果了解两种策略的优劣")

        print("\n感谢使用卡尔曼滤波器配对交易策略！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
