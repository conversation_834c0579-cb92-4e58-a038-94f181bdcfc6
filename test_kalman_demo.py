#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的卡尔曼滤波器演示脚本

这个脚本用于验证kalman_demo.py的数据加载和基本功能是否正常工作
"""

import sys
import os
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加src目录到Python路径
sys.path.append('src')

def test_data_loading():
    """测试数据加载功能"""
    print("测试数据加载功能...")
    
    try:
        # 导入数据加载相关模块
        from src.data.loader import DataLoader
        
        # 回测参数
        BACKTEST_PARAMS = {
            'start_date': '2023-11-27',
            'end_date': '2024-11-25',
            'data_frequency': 'minute'
        }
        
        # 初始化数据加载器
        data_loader = DataLoader()
        
        # 检查数据目录
        data_dir = 'data'
        if not os.path.exists(data_dir):
            print(f"数据目录 {data_dir} 不存在")
            return None
        
        # 列出数据文件
        data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        print(f"找到数据文件: {data_files}")
        
        if not data_files:
            print("未找到数据文件，将使用模拟数据")
            return None
        
        # 尝试加载数据
        try:
            print("尝试加载真实数据...")
            data = data_loader.load_data(
                start_date=BACKTEST_PARAMS['start_date'],
                end_date=BACKTEST_PARAMS['end_date'],
                frequency=BACKTEST_PARAMS['data_frequency']
            )
            
            print(f"✓ 成功加载真实数据")
            print(f"  数据形状: {data.shape}")
            print(f"  数据列: {list(data.columns)}")
            print(f"  数据期间: {data.index[0]} 到 {data.index[-1]}")
            
            return data
            
        except Exception as e:
            print(f"✗ 加载真实数据失败: {e}")
            return None
            
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        return None

def test_kalman_strategy_basic():
    """测试卡尔曼滤波器策略基本功能"""
    print("\n测试卡尔曼滤波器策略基本功能...")
    
    try:
        from src.strategy.kalman_pairs_strategy import KalmanPairsStrategy
        
        # 创建模拟数据
        np.random.seed(42)
        dates = pd.date_range(start='2024-01-01', periods=100, freq='T')
        
        returns1 = np.random.normal(0.001, 0.02, 100)
        returns2 = 0.8 * returns1 + 0.6 * np.random.normal(0.0008, 0.015, 100)
        
        price1 = 100 * np.exp(np.cumsum(returns1))
        price2 = 95 * np.exp(np.cumsum(returns2))
        
        data = pd.DataFrame({
            '518880': price1,
            '518850': price2
        }, index=dates)
        
        print(f"创建测试数据: {data.shape}")
        
        # 创建策略
        strategy = KalmanPairsStrategy(
            process_noise_ratio=1e-4,
            measurement_noise_ratio=1e-2,
            trading_threshold=2.0,
            verbose=False
        )
        
        # 模拟引擎
        class MockEngine:
            def __init__(self, data):
                self.data = data
                self.commission_rate = 0.0001
                self.slippage_rate = 0.0001
                self.portfolio = {'positions': {}, 'cash': 100000}
        
        engine = MockEngine(data)
        
        # 初始化策略
        strategy.initialize(engine)
        
        print(f"✓ 策略初始化成功")
        print(f"  信号数据形状: {strategy.signals.shape}")
        print(f"  交易信号数: {sum(strategy.signals['trade_signal'] != 0)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 卡尔曼滤波器策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backtest_engine():
    """测试回测引擎"""
    print("\n测试回测引擎...")
    
    try:
        from src.strategy.kalman_pairs_strategy import KalmanPairsStrategy
        from src.engine.engine import BacktestEngine
        
        # 创建小规模测试数据
        np.random.seed(42)
        dates = pd.date_range(start='2024-01-01', periods=50, freq='T')
        
        returns1 = np.random.normal(0.001, 0.02, 50)
        returns2 = 0.8 * returns1 + 0.6 * np.random.normal(0.0008, 0.015, 50)
        
        price1 = 100 * np.exp(np.cumsum(returns1))
        price2 = 95 * np.exp(np.cumsum(returns2))
        
        data = pd.DataFrame({
            '518880': price1,
            '518850': price2
        }, index=dates)
        
        # 创建策略
        strategy = KalmanPairsStrategy(
            process_noise_ratio=1e-4,
            measurement_noise_ratio=1e-2,
            trading_threshold=1.5,  # 降低阈值以产生更多信号
            verbose=False
        )
        
        # 创建回测引擎
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=100000,
            commission_rate=0.0001,
            slippage_rate=0.0001,
            verbose=False
        )
        
        # 运行回测
        results = engine.run()
        
        print(f"✓ 回测引擎运行成功")
        print(f"  交易记录数: {len(results['trades'])}")
        print(f"  最终权益: {results['equity_curve'][-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 回测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_analysis():
    """测试性能分析功能"""
    print("\n测试性能分析功能...")
    
    try:
        from src.analysis.performance import PerformanceAnalyzer
        
        # 创建模拟回测结果
        dates = pd.date_range(start='2024-01-01', periods=100, freq='T')
        equity_curve = [100000 * (1 + 0.001 * i + np.random.normal(0, 0.01)) for i in range(100)]
        
        results = {
            'equity_curve': equity_curve,
            'daily_returns': [0.001] * 99,
            'trades': [
                {'datetime': dates[10], 'symbol': '518880', 'direction': 1, 'volume': 100, 'price': 100},
                {'datetime': dates[20], 'symbol': '518880', 'direction': -1, 'volume': 100, 'price': 101}
            ]
        }
        
        backtest_params = {
            'initial_capital': 100000,
            'data_frequency': 'minute'
        }
        
        # 创建分析器
        analyzer = PerformanceAnalyzer(results, backtest_params=backtest_params)
        performance_metrics = analyzer.analyze()
        
        print(f"✓ 性能分析成功")
        print(f"  指标数量: {len(performance_metrics)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 性能分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("卡尔曼滤波器演示脚本测试")
    print("=" * 50)
    
    # 创建测试结果目录
    os.makedirs("test_results", exist_ok=True)
    
    test_results = []
    
    # 运行各项测试
    print("1. 测试数据加载...")
    data = test_data_loading()
    test_results.append(("数据加载", data is not None))
    
    print("\n2. 测试卡尔曼滤波器策略...")
    kalman_test = test_kalman_strategy_basic()
    test_results.append(("卡尔曼策略", kalman_test))
    
    print("\n3. 测试回测引擎...")
    engine_test = test_backtest_engine()
    test_results.append(("回测引擎", engine_test))
    
    print("\n4. 测试性能分析...")
    analysis_test = test_performance_analysis()
    test_results.append(("性能分析", analysis_test))
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有测试通过！kalman_demo.py 应该可以正常运行")
        print("您现在可以运行: python kalman_demo.py")
    else:
        print("✗ 部分测试失败，请检查相关模块")
        
    if data is not None:
        print(f"\n发现真实数据，数据量: {len(data)} 条记录")
        print("建议直接运行完整演示脚本")
    else:
        print("\n未找到真实数据，演示脚本将使用模拟数据")

if __name__ == "__main__":
    main()
