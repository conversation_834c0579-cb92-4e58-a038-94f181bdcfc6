================================================================================
卡尔曼滤波器 vs 滚动回归 性能基准测试报告
================================================================================

1. 计算时间对比
----------------------------------------
数据大小: 58080 个数据点
卡尔曼滤波器总时间: 3.3447秒

滚动回归(窗口10): 6.1901秒 (慢1.9倍)
滚动回归(窗口20): 5.6252秒 (慢1.7倍)
滚动回归(窗口40): 5.4011秒 (慢1.6倍)
滚动回归(窗口60): 5.7263秒 (慢1.7倍)

2. 内存使用对比
----------------------------------------
卡尔曼滤波器内存使用: 43.63 MB
滚动回归(窗口10): 13.94 MB
滚动回归(窗口20): 13.75 MB
滚动回归(窗口40): 13.75 MB

3. 实时处理能力对比
----------------------------------------
批处理大小 1:
  滚动回归吞吐量: 10634.0 点/秒
  卡尔曼滤波器吞吐量: 13349.3 点/秒
  性能提升: 1.3倍

批处理大小 10:
  滚动回归吞吐量: 16546.2 点/秒
  卡尔曼滤波器吞吐量: 22571.3 点/秒
  性能提升: 1.4倍

批处理大小 100:
  滚动回归吞吐量: 18415.8 点/秒
  卡尔曼滤波器吞吐量: 24024.6 点/秒
  性能提升: 1.3倍

4. 总结
----------------------------------------
卡尔曼滤波器相比滚动回归的优势:
• 计算复杂度从O(n*w)降低到O(n)
• 内存使用更少，不需要存储滚动窗口数据
• 实时处理能力更强，适合高频交易
• 能够动态调整模型参数
• 提供不确定性估计