# 卡尔曼滤波器配对交易策略使用指南

## 🚀 快速开始

### 1. 运行完整演示
```bash
python kalman_demo.py
```

这个脚本会：
- 自动加载本地ETF数据（使用DataLoader）
- 运行卡尔曼滤波器策略回测
- 运行传统滚动回归策略回测
- 进行性能基准测试
- 生成可视化图表
- 创建分析报告

### 2. 快速测试
```bash
python test_kalman_demo.py
```

验证所有组件是否正常工作。

## 📊 演示结果说明

### 成功运行后，您将看到以下结果：

#### 1. 数据加载信息
```
正在使用DataLoader加载真实数据...
检查本地数据文件...
加载回测数据...
成功加载真实数据
数据形状: (58080, 2)
数据列: ['518850', '518880']
数据期间: 2023-11-27 09:30:00 到 2024-11-22 15:00:00
数据频率: minute
```

#### 2. 卡尔曼滤波器策略运行
```
运行卡尔曼滤波器策略
============================================================
卡尔曼滤波器策略初始化完成
ETF对: ['518850', '518880']
数据期间: 2023-11-27 09:30:00 到 2024-11-22 15:00:00
总数据点: 58080
卡尔曼滤波器信号计算完成，耗时: 3.0287秒
生成交易信号数: 1234
```

#### 3. 性能基准测试结果
```
运行性能基准测试
============================================================
1. 计算时间基准测试...
数据大小: 58080 个数据点
卡尔曼滤波器: 3.3447秒 (0.06ms/点)
  窗口10: 6.1901秒 (0.11ms/点)
  窗口20: 5.6252秒 (0.10ms/点)
  窗口40: 5.4011秒 (0.09ms/点)
  窗口60: 5.7263秒 (0.10ms/点)
  相比窗口10的滚动回归，卡尔曼滤波器快 1.9x
  相比窗口20的滚动回归，卡尔曼滤波器快 1.7x
  相比窗口40的滚动回归，卡尔曼滤波器快 1.6x
  相比窗口60的滚动回归，卡尔曼滤波器快 1.7x
```

## 📁 生成的文件结构

```
results/
├── 卡尔曼滤波器/                    # 卡尔曼滤波器回测结果
│   ├── backtest_analysis_*.png      # 回测分析图表
│   └── backtest_report_*.csv        # 回测性能报告
├── 滚动回归/                        # 滚动回归回测结果
│   ├── backtest_analysis_*.png      # 回测分析图表
│   └── backtest_report_*.csv        # 回测性能报告
├── visualizations/                  # 卡尔曼滤波器专用可视化
│   ├── kalman_dashboard.png         # 综合仪表板
│   ├── kalman_price_ratio.png       # 价格比率分析
│   ├── kalman_uncertainty.png       # 不确定性演化
│   ├── kalman_innovation.png        # 创新序列分析
│   ├── kalman_state_evolution.png   # 滤波器状态演化
│   └── performance_comparison.png   # 性能对比图
├── kalman/                          # 卡尔曼滤波器策略数据
│   ├── kalman_signals_*.csv         # 信号数据
│   └── kalman_orders_*.csv          # 订单记录
├── rolling/                         # 滚动回归策略数据
│   ├── signals_*.csv                # 信号数据
│   ├── orders_*.csv                 # 订单记录
│   └── trades_*.csv                 # 交易记录
├── performance_report.txt           # 性能基准测试报告
├── summary_report.txt               # 总结报告
└── strategy_comparison.csv          # 策略对比结果
```

## 🔧 参数调优

### 卡尔曼滤波器参数

在 `kalman_demo.py` 中修改以下参数：

```python
kalman_strategy = KalmanPairsStrategy(
    process_noise_ratio=1e-4,      # 过程噪声比率
    measurement_noise_ratio=1e-2,  # 测量噪声比率
    trading_threshold=1.5,         # 交易阈值
    max_pos_size=1.0,             # 最大持仓比例
    verbose=True
)
```

#### 参数说明：

1. **process_noise_ratio** (1e-5 到 1e-3)
   - 控制模型对新信息的敏感度
   - 值越大，模型越敏感，适应性越强
   - 市场波动大时可以增加

2. **measurement_noise_ratio** (1e-3 到 1e-1)
   - 反映观测数据的可靠性
   - 值越大，表示对观测数据的信任度越低
   - 数据质量差时可以增加

3. **trading_threshold** (1.0 到 4.0)
   - 交易信号的触发阈值（标准差倍数）
   - 值越小，交易越频繁
   - 值越大，交易越保守

### 回测参数

在 `kalman_demo.py` 顶部修改：

```python
BACKTEST_PARAMS = {
    'start_date': '2023-11-27',     # 回测开始日期
    'end_date': '2024-11-25',       # 回测结束日期
    'initial_capital': 100000,      # 初始资金
    'data_frequency': 'minute',     # 数据频率
    'commission_rate': 0.0001,      # 手续费率
    'slippage_rate': 0.0001         # 滑点率
}
```

## 📈 结果解读

### 1. 性能指标对比

查看 `results/strategy_comparison.csv` 了解两种策略的详细对比：

- **总收益率**: 策略的总体收益表现
- **夏普比率**: 风险调整后的收益
- **最大回撤**: 最大损失幅度
- **交易次数**: 交易频率
- **年化收益率**: 年化后的收益表现

### 2. 计算性能优势

从 `results/performance_report.txt` 可以看到：

- **计算时间**: 卡尔曼滤波器比滚动回归快1.6-1.9倍
- **实时处理**: 卡尔曼滤波器吞吐量提升1.3-1.4倍
- **内存使用**: 根据数据量不同有所差异

### 3. 可视化分析

查看 `results/visualizations/` 目录中的图表：

- **kalman_dashboard.png**: 综合仪表板，展示所有关键信息
- **kalman_price_ratio.png**: 价格比率与滤波结果对比
- **kalman_uncertainty.png**: 不确定性随时间的变化
- **kalman_innovation.png**: 创新序列的统计特性
- **kalman_state_evolution.png**: 滤波器内部状态演化

## ⚠️ 注意事项

### 1. 数据要求
- 确保 `data/` 目录中有ETF价格数据
- 数据格式应为CSV，包含时间戳和价格列
- 建议数据量不少于1000个数据点

### 2. 交易信号调优
- 如果交易次数为0，降低 `trading_threshold`
- 如果交易过于频繁，提高 `trading_threshold`
- 根据市场特性调整噪声参数

### 3. 性能考虑
- 大数据量时卡尔曼滤波器优势更明显
- 小数据量时两种方法性能差异不大
- 实时交易场景下卡尔曼滤波器更适合

## 🔍 故障排除

### 常见问题

1. **数据加载失败**
   ```
   解决方案: 检查data/目录是否存在，确保有ETF数据文件
   ```

2. **交易信号为0**
   ```
   解决方案: 降低trading_threshold参数，从2.0改为1.5或1.0
   ```

3. **可视化图表无法生成**
   ```
   解决方案: 确保安装了matplotlib和seaborn库
   pip install matplotlib seaborn
   ```

4. **性能分析失败**
   ```
   解决方案: 检查src/analysis/目录下的模块是否完整
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境和依赖库是否正确安装
2. 数据文件格式是否正确
3. 参数设置是否合理
4. 查看控制台输出的错误信息

---

**祝您使用愉快！卡尔曼滤波器配对交易策略为您的量化交易带来新的可能性。**
