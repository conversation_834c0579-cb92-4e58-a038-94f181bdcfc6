import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from typing import Dict, List, Optional, Tuple
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class KalmanVisualizer:
    """
    卡尔曼滤波器策略可视化工具
    
    提供以下可视化功能：
    1. 价格比率和滤波结果对比
    2. 不确定性变化
    3. 交易信号可视化
    4. 滤波器状态监控
    5. 创新序列分析
    6. 性能对比图表
    """
    
    def __init__(self, output_dir: str = "visualizations"):
        """
        初始化可视化工具
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置绘图样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def plot_price_ratio_and_filter(
        self, 
        signals: pd.DataFrame, 
        title: str = "价格比率与卡尔曼滤波结果",
        save_name: str = "price_ratio_filter.png"
    ) -> None:
        """
        绘制价格比率和滤波结果对比图
        
        Args:
            signals: 信号数据DataFrame
            title: 图表标题
            save_name: 保存文件名
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
        
        # 上图：价格比率、滤波结果和交易边界
        ax1.plot(signals.index, signals['price_ratio'], 
                label='实际价格比率', alpha=0.7, linewidth=1)
        ax1.plot(signals.index, signals['filtered_ratio'], 
                label='卡尔曼滤波结果', linewidth=2, color='red')
        
        # 绘制不确定性带
        upper_band = signals['filtered_ratio'] + signals['uncertainty']
        lower_band = signals['filtered_ratio'] - signals['uncertainty']
        ax1.fill_between(signals.index, upper_band, lower_band, 
                        alpha=0.2, color='red', label='不确定性带(±1σ)')
        
        # 绘制交易边界
        if 'upper_bound' in signals.columns and 'lower_bound' in signals.columns:
            ax1.plot(signals.index, signals['upper_bound'], 
                    '--', color='green', alpha=0.8, label='上交易边界')
            ax1.plot(signals.index, signals['lower_bound'], 
                    '--', color='green', alpha=0.8, label='下交易边界')
        
        ax1.set_ylabel('价格比率')
        ax1.set_title(title)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：交易信号
        signal_colors = {1: 'green', -1: 'red', 0: 'gray'}
        for signal_value, color in signal_colors.items():
            mask = signals['trade_signal'] == signal_value
            if mask.any():
                signal_name = {1: '买入ETF1', -1: '卖出ETF1', 0: '无信号'}[signal_value]
                ax2.scatter(signals.index[mask], signals['trade_signal'][mask], 
                           c=color, alpha=0.6, s=30, label=signal_name)
        
        ax2.set_ylabel('交易信号')
        ax2.set_xlabel('时间')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(-1.5, 1.5)
        
        # 格式化x轴
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator())
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_uncertainty_evolution(
        self, 
        signals: pd.DataFrame,
        title: str = "不确定性演化",
        save_name: str = "uncertainty_evolution.png"
    ) -> None:
        """
        绘制不确定性演化图
        
        Args:
            signals: 信号数据DataFrame
            title: 图表标题
            save_name: 保存文件名
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8), sharex=True)
        
        # 上图：不确定性时间序列
        ax1.plot(signals.index, signals['uncertainty'], 
                color='blue', linewidth=1.5, label='不确定性')
        ax1.fill_between(signals.index, 0, signals['uncertainty'], 
                        alpha=0.3, color='blue')
        ax1.set_ylabel('不确定性')
        ax1.set_title(title)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：不确定性分布直方图
        ax2.hist(signals['uncertainty'].dropna(), bins=50, 
                alpha=0.7, color='blue', edgecolor='black')
        ax2.set_xlabel('不确定性值')
        ax2.set_ylabel('频次')
        ax2.set_title('不确定性分布')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        uncertainty_stats = signals['uncertainty'].describe()
        stats_text = f"均值: {uncertainty_stats['mean']:.4f}\n"
        stats_text += f"标准差: {uncertainty_stats['std']:.4f}\n"
        stats_text += f"中位数: {uncertainty_stats['50%']:.4f}"
        ax2.text(0.7, 0.7, stats_text, transform=ax2.transAxes, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_innovation_analysis(
        self, 
        kalman_filter,
        title: str = "创新序列分析",
        save_name: str = "innovation_analysis.png"
    ) -> None:
        """
        绘制创新序列分析图
        
        Args:
            kalman_filter: 卡尔曼滤波器实例
            title: 图表标题
            save_name: 保存文件名
        """
        if not kalman_filter.history['innovations']:
            print("没有创新数据可绘制")
            return
        
        innovations = np.array(kalman_filter.history['innovations'])
        innovation_vars = np.array(kalman_filter.history['innovation_covariances'])
        timestamps = kalman_filter.history['timestamps']
        
        # 标准化创新
        standardized_innovations = innovations / np.sqrt(innovation_vars)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 左上：创新时间序列
        ax1.plot(timestamps, innovations, linewidth=1, alpha=0.7)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax1.set_ylabel('创新值')
        ax1.set_title('创新序列')
        ax1.grid(True, alpha=0.3)
        
        # 右上：标准化创新时间序列
        ax2.plot(timestamps, standardized_innovations, linewidth=1, alpha=0.7, color='green')
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax2.axhline(y=2, color='orange', linestyle='--', alpha=0.5, label='±2σ')
        ax2.axhline(y=-2, color='orange', linestyle='--', alpha=0.5)
        ax2.set_ylabel('标准化创新')
        ax2.set_title('标准化创新序列')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 左下：创新分布
        ax3.hist(standardized_innovations, bins=30, alpha=0.7, 
                density=True, color='blue', edgecolor='black')
        
        # 叠加标准正态分布
        x = np.linspace(-4, 4, 100)
        ax3.plot(x, (1/np.sqrt(2*np.pi)) * np.exp(-0.5*x**2), 
                'r-', linewidth=2, label='标准正态分布')
        ax3.set_xlabel('标准化创新')
        ax3.set_ylabel('密度')
        ax3.set_title('创新分布 vs 标准正态分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 右下：Q-Q图
        from scipy import stats
        stats.probplot(standardized_innovations, dist="norm", plot=ax4)
        ax4.set_title('Q-Q图 (正态性检验)')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_filter_state_evolution(
        self, 
        kalman_filter,
        title: str = "滤波器状态演化",
        save_name: str = "filter_state_evolution.png"
    ) -> None:
        """
        绘制滤波器状态演化图
        
        Args:
            kalman_filter: 卡尔曼滤波器实例
            title: 图表标题
            save_name: 保存文件名
        """
        if not kalman_filter.history['states']:
            print("没有状态数据可绘制")
            return
        
        states = kalman_filter.history['states']
        timestamps = kalman_filter.history['timestamps']
        
        # 提取状态变量
        price_ratios = [state[0, 0] for state in states]
        velocities = [state[1, 0] for state in states]
        
        # 提取协方差对角元素
        covariances = kalman_filter.history['covariances']
        ratio_uncertainties = [np.sqrt(cov[0, 0]) for cov in covariances]
        velocity_uncertainties = [np.sqrt(cov[1, 1]) for cov in covariances]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 左上：价格比率状态
        ax1.plot(timestamps, price_ratios, linewidth=2, label='滤波价格比率')
        ax1.fill_between(timestamps, 
                        np.array(price_ratios) - np.array(ratio_uncertainties),
                        np.array(price_ratios) + np.array(ratio_uncertainties),
                        alpha=0.3, label='不确定性带')
        ax1.set_ylabel('价格比率')
        ax1.set_title('价格比率状态估计')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 右上：速度状态
        ax2.plot(timestamps, velocities, linewidth=2, color='green', label='价格比率变化速度')
        ax2.fill_between(timestamps,
                        np.array(velocities) - np.array(velocity_uncertainties),
                        np.array(velocities) + np.array(velocity_uncertainties),
                        alpha=0.3, color='green', label='不确定性带')
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax2.set_ylabel('速度')
        ax2.set_title('速度状态估计')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 左下：状态空间轨迹
        ax3.plot(price_ratios, velocities, alpha=0.7, linewidth=1)
        ax3.scatter(price_ratios[0], velocities[0], color='green', s=100, label='起始点')
        ax3.scatter(price_ratios[-1], velocities[-1], color='red', s=100, label='结束点')
        ax3.set_xlabel('价格比率')
        ax3.set_ylabel('速度')
        ax3.set_title('状态空间轨迹')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 右下：协方差矩阵演化
        correlations = []
        for cov in covariances:
            if cov[0, 0] > 0 and cov[1, 1] > 0:
                corr = cov[0, 1] / (np.sqrt(cov[0, 0]) * np.sqrt(cov[1, 1]))
                correlations.append(corr)
            else:
                correlations.append(0)
        
        ax4.plot(timestamps, correlations, linewidth=2, color='purple')
        ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax4.set_ylabel('相关系数')
        ax4.set_xlabel('时间')
        ax4.set_title('状态变量相关性')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_performance_comparison(
        self, 
        benchmark_results: Dict,
        title: str = "性能对比",
        save_name: str = "performance_comparison.png"
    ) -> None:
        """
        绘制性能对比图
        
        Args:
            benchmark_results: 基准测试结果
            title: 图表标题
            save_name: 保存文件名
        """
        if 'computation_time' not in benchmark_results:
            print("没有计算时间数据可绘制")
            return
        
        ct_results = benchmark_results['computation_time']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：计算时间对比
        windows = list(ct_results['rolling_regression'].keys())
        rolling_times = [ct_results['rolling_regression'][w]['computation_time'] for w in windows]
        kalman_time = ct_results['kalman_filter']['computation_time']
        
        x_pos = np.arange(len(windows))
        ax1.bar(x_pos, rolling_times, alpha=0.7, label='滚动回归', color='blue')
        ax1.axhline(y=kalman_time, color='red', linestyle='--', 
                   linewidth=2, label=f'卡尔曼滤波器 ({kalman_time:.4f}s)')
        
        ax1.set_xlabel('滚动窗口大小')
        ax1.set_ylabel('计算时间 (秒)')
        ax1.set_title('计算时间对比')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(windows)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 右图：性能提升倍数
        speedups = [ct_results['performance_improvement'][w]['speedup_factor'] for w in windows]
        ax2.bar(x_pos, speedups, alpha=0.7, color='green')
        ax2.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='基准线')
        
        ax2.set_xlabel('滚动窗口大小')
        ax2.set_ylabel('性能提升倍数')
        ax2.set_title('卡尔曼滤波器性能提升')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(windows)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, speedup in enumerate(speedups):
            ax2.text(i, speedup + 0.1, f'{speedup:.1f}x', 
                    ha='center', va='bottom', fontweight='bold')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
    
    def create_comprehensive_dashboard(
        self, 
        signals: pd.DataFrame,
        kalman_filter,
        benchmark_results: Optional[Dict] = None,
        save_name: str = "kalman_dashboard.png"
    ) -> None:
        """
        创建综合仪表板
        
        Args:
            signals: 信号数据DataFrame
            kalman_filter: 卡尔曼滤波器实例
            benchmark_results: 基准测试结果（可选）
            save_name: 保存文件名
        """
        fig = plt.figure(figsize=(20, 15))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 价格比率和滤波结果 (占据上方两列)
        ax1 = fig.add_subplot(gs[0, :2])
        ax1.plot(signals.index, signals['price_ratio'], 
                label='实际价格比率', alpha=0.7, linewidth=1)
        ax1.plot(signals.index, signals['filtered_ratio'], 
                label='卡尔曼滤波结果', linewidth=2, color='red')
        
        upper_band = signals['filtered_ratio'] + signals['uncertainty']
        lower_band = signals['filtered_ratio'] - signals['uncertainty']
        ax1.fill_between(signals.index, upper_band, lower_band, 
                        alpha=0.2, color='red', label='不确定性带')
        
        ax1.set_title('价格比率与卡尔曼滤波结果', fontsize=14)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 不确定性演化 (右上)
        ax2 = fig.add_subplot(gs[0, 2])
        ax2.plot(signals.index, signals['uncertainty'], color='blue', linewidth=1.5)
        ax2.fill_between(signals.index, 0, signals['uncertainty'], alpha=0.3, color='blue')
        ax2.set_title('不确定性演化', fontsize=14)
        ax2.grid(True, alpha=0.3)
        
        # 3. 交易信号 (中间左)
        ax3 = fig.add_subplot(gs[1, 0])
        signal_colors = {1: 'green', -1: 'red', 0: 'gray'}
        for signal_value, color in signal_colors.items():
            mask = signals['trade_signal'] == signal_value
            if mask.any():
                ax3.scatter(signals.index[mask], signals['trade_signal'][mask], 
                           c=color, alpha=0.6, s=20)
        ax3.set_title('交易信号', fontsize=14)
        ax3.set_ylim(-1.5, 1.5)
        ax3.grid(True, alpha=0.3)
        
        # 4. 创新分布 (中间中)
        if kalman_filter.history['innovations']:
            ax4 = fig.add_subplot(gs[1, 1])
            innovations = np.array(kalman_filter.history['innovations'])
            innovation_vars = np.array(kalman_filter.history['innovation_covariances'])
            standardized_innovations = innovations / np.sqrt(innovation_vars)
            
            ax4.hist(standardized_innovations, bins=30, alpha=0.7, density=True, color='blue')
            x = np.linspace(-4, 4, 100)
            ax4.plot(x, (1/np.sqrt(2*np.pi)) * np.exp(-0.5*x**2), 'r-', linewidth=2)
            ax4.set_title('标准化创新分布', fontsize=14)
            ax4.grid(True, alpha=0.3)
        
        # 5. 状态空间轨迹 (中间右)
        if kalman_filter.history['states']:
            ax5 = fig.add_subplot(gs[1, 2])
            states = kalman_filter.history['states']
            price_ratios = [state[0, 0] for state in states]
            velocities = [state[1, 0] for state in states]
            
            ax5.plot(price_ratios, velocities, alpha=0.7, linewidth=1)
            ax5.scatter(price_ratios[0], velocities[0], color='green', s=50, label='起始')
            ax5.scatter(price_ratios[-1], velocities[-1], color='red', s=50, label='结束')
            ax5.set_xlabel('价格比率')
            ax5.set_ylabel('速度')
            ax5.set_title('状态空间轨迹', fontsize=14)
            ax5.legend()
            ax5.grid(True, alpha=0.3)
        
        # 6. 性能统计 (下方)
        ax6 = fig.add_subplot(gs[2, :])
        ax6.axis('off')
        
        # 创建统计信息表格
        stats_text = "卡尔曼滤波器策略统计摘要\n"
        stats_text += "=" * 50 + "\n"
        
        # 基本统计
        stats_text += f"数据点数: {len(signals)}\n"
        stats_text += f"交易信号数: {sum(signals['trade_signal'] != 0)}\n"
        stats_text += f"平均不确定性: {signals['uncertainty'].mean():.4f}\n"
        stats_text += f"不确定性标准差: {signals['uncertainty'].std():.4f}\n"
        
        # 滤波器状态
        if kalman_filter.is_initialized:
            state_summary = kalman_filter.get_state_summary()
            stats_text += f"当前价格比率估计: {state_summary['price_ratio']:.4f}\n"
            stats_text += f"当前速度估计: {state_summary['velocity']:.4f}\n"
            stats_text += f"总观测数: {state_summary['total_observations']}\n"
        
        # 性能对比
        if benchmark_results and 'computation_time' in benchmark_results:
            ct_results = benchmark_results['computation_time']
            kalman_time = ct_results['kalman_filter']['computation_time']
            stats_text += f"\n性能优势:\n"
            stats_text += f"卡尔曼滤波器计算时间: {kalman_time:.4f}秒\n"
            
            for window in sorted(ct_results['rolling_regression'].keys()):
                speedup = ct_results['performance_improvement'][window]['speedup_factor']
                stats_text += f"相比窗口{window}滚动回归快: {speedup:.1f}倍\n"
        
        ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle('卡尔曼滤波器配对交易策略综合仪表板', fontsize=18, fontweight='bold')
        plt.savefig(os.path.join(self.output_dir, save_name), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"综合仪表板已保存到: {os.path.join(self.output_dir, save_name)}")
