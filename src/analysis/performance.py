import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union

class PerformanceAnalyzer:
    """回测结果分析器"""
    
    def __init__(self, results: Dict, backtest_params: Optional[Dict] = None):
        """
        初始化分析器
        Args:
            results: 回测结果字典
            backtest_params: 回测参数字典，包含data_frequency等参数
        """
        self.results = results
        self.equity_curve = np.array(results['equity_curve'])
        self.daily_returns = np.array(results['daily_returns'])
        self.trades = results.get('trades', [])
        self.positions = results.get('positions', [])
        
        # 获取数据频率（优先使用backtest_params中的设置）
        self.data_frequency = (backtest_params.get('data_frequency') if backtest_params 
                             else results.get('params', {}).get('data_frequency', 'day'))
        
        # 计算年化因子
        self.annualization_factor = self._calculate_annualization_factor()
        
    
    def _calculate_annualization_factor(self) -> float:
        """
        根据数据频率计算年化因子
        Returns:
            float: 年化因子
        """
        # 基础年化因子（交易日数）
        base_factor = 252
        
        # 根据数据频率调整年化因子
        if self.data_frequency == 'minute':
            # 假设每天交易4小时，每小时60分钟
            return base_factor * 240  # 252 * (4 * 60)
        elif self.data_frequency == 'hour':
            # 假设每天交易4小时
            return base_factor * 4
        elif self.data_frequency == 'tick':
            # 假设每分钟20个tick，每天交易4小时
            return base_factor * 240 * 20
        else:  # 'day' or default
            return base_factor
    
    def analyze_etf_holdings(self) -> Dict:
        """分析ETF持仓变化"""
        # 如果没有交易记录，返回默认值
        if not self.trades:
            return self._get_default_etf_analysis()

        # 获取所有交易中的ETF代码
        unique_symbols = set(trade['symbol'] for trade in self.trades)
        if not unique_symbols:
            return self._get_default_etf_analysis()
            
        # 使用第一个交易的ETF代码
        etf1_symbol = list(unique_symbols)[0]
        
        # 按时间排序交易记录
        sorted_trades = sorted(self.trades, key=lambda x: x['datetime'])
        
        # 记录ETF1的持仓变化
        holdings = []
        dates = []
        current_holdings = 0
        winning_trades = -1
        last_holding = 0
        
        # 遍历所有交易，记录ETF1的持仓变化
        for trade in sorted_trades:
            if trade['symbol'] == etf1_symbol:
                # 先记录上一次持仓
                if len(holdings)<2:
                    last_holding = 0
                else:
                    last_holding = holdings[len(holdings)-2]
                
                # 更新当前持仓
                current_holdings += trade['volume'] * trade['direction']
                holdings.append(current_holdings)
                dates.append(pd.to_datetime(trade['datetime']))
                
                # 计算成功交易（持仓增加）
                if current_holdings > last_holding:
                    winning_trades += 1
        
        # 如果没有有效的持仓记录，返回默认值
        if not holdings:
            return self._get_default_etf_analysis(etf1_symbol)
        
        # 计算交易对数（买入和卖出配对）
        total_trade_pairs = max(len(holdings) // 2, 1)  # 确保至少为1，避免除零
        
        # 计算换仓胜率
        win_rate = winning_trades / total_trade_pairs if total_trade_pairs > 0 else 0

        # 计算平均换仓收益率
        holding_returns = []
        for i in range(2, len(holdings)):
            if holdings[i-2] != 0:  # 避免除以零
                holding_return = (holdings[i] - holdings[i-2]) / abs(holdings[i-2])
                holding_returns.append(holding_return)
        
        avg_holding_return = np.mean(holding_returns) if holding_returns else 0

        # 过滤掉0值持仓，只计算有效持仓的变化
        valid_holdings = [h for h in holdings if h != 0]

        # 计算年化持仓增长率
        annual_holdings_growth = 0
        if len(valid_holdings) >= 2:
            # 计算总天数
            total_days = (dates[-1] - dates[0]).days
            if total_days > 0:
                # 计算总增长率
                total_growth = valid_holdings[-1] / valid_holdings[0] - 1
                # 计算年化因子
                annualization_factor = 252 / total_days
                # 计算年化增长率
                annual_holdings_growth = (1 + total_growth) ** annualization_factor - 1
                
        # 计算持仓量波动指标（类似夏普比率）
        holdings_returns = []
        prev_holding = None
        
        # 计算持仓量相对变化
        for i in range(1, len(valid_holdings)):
            if valid_holdings[i-1] != 0:  # 避免除以零
                holding_return = (valid_holdings[i] - valid_holdings[i-1]) / abs(valid_holdings[i-1])
                holdings_returns.append(holding_return)
        
        # 计算类夏普比率
        holdings_volatility_ratio = 0
        if len(holdings_returns) >= 2:  # 至少需要两个数据点
            # 计算持仓变化的标准差
            holdings_std = np.std(holdings_returns)
            
            if holdings_std > 0:  # 避免除以零
                # 使用年化持仓增长率作为收益率
                holdings_volatility_ratio = annual_holdings_growth/len(holdings_returns) / holdings_std
                
                # 使用与回测相同的年化因子进行调整
                holdings_volatility_ratio *= np.sqrt(self.annualization_factor)
        
        return {
            'etf1_symbol': etf1_symbol,
            'holdings': holdings,
            'dates': dates,
            'total_trade_pairs': total_trade_pairs,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'avg_holding_return': avg_holding_return,
            'annual_holdings_growth': annual_holdings_growth,
            'holdings_volatility_ratio': holdings_volatility_ratio
        }
    
    def _get_default_etf_analysis(self, etf1_symbol='Unknown') -> Dict:
        """返回默认的ETF分析结果"""
        return {
            'etf1_symbol': etf1_symbol,
            'holdings': [],
            'dates': [],
            'total_trade_pairs': 0,
            'winning_trades': 0,
            'win_rate': 0.0,
            'avg_holding_return': 0.0,
            'annual_holdings_growth': 0.0,
            'holdings_volatility_ratio': 0.0
        }
    
    def analyze(self) -> pd.DataFrame:
        """分析回测结果并生成报告"""
        metrics = {}
        
        # 计算总收益率
        total_return = (self.equity_curve[-1] - self.equity_curve[0]) / self.equity_curve[0]
        metrics['总收益率'] = f"{total_return:.2%}"
        
        # 计算年化收益率
        minutes = len(self.daily_returns)
        print(minutes , self.annualization_factor)
        annual_return = (1 + total_return) ** (self.annualization_factor/minutes) - 1
        metrics['年化收益率'] = f"{annual_return:.2%}"
        
        # 计算夏普比率
        sharpe_ratio = 0
        if len(self.daily_returns) > 0:
            avg_return = np.mean(self.daily_returns)
            std_return = np.std(self.daily_returns)
            if std_return > 0:
                sharpe_ratio = np.sqrt(self.annualization_factor) * avg_return / std_return
        metrics['夏普比率'] = f"{sharpe_ratio:.4f}"
        
        # 计算最大回撤
        max_drawdown = 0
        peak = self.equity_curve[0]
        for value in self.equity_curve:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak if peak > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)
        metrics['最大回撤'] = f"{max_drawdown:.2%}"
        
        # 交易统计
        if self.trades:
            metrics['总交易次数'] = f"{len(self.trades)}"
            # 检查交易记录是否包含commission字段
            if self.trades and 'commission' in self.trades[0]:
                total_commission = sum(trade['commission'] for trade in self.trades)
                metrics['总手续费'] = f"{total_commission:.2f}"
            else:
                metrics['总手续费'] = "N/A"
        else:
            metrics['总交易次数'] = "0"
            metrics['总手续费'] = "0.00"
        
        # 计算波动率
        volatility = np.std(self.daily_returns) * np.sqrt(self.annualization_factor) if len(self.daily_returns) > 0 else 0
        metrics['年化波动率'] = f"{volatility:.2%}"
        
        # 计算下行波动率
        downside_returns = self.daily_returns[self.daily_returns < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(self.annualization_factor) if len(downside_returns) > 0 else 0
        metrics['下行波动率'] = f"{downside_volatility:.2%}"
        
        # 计算索提诺比率
        sortino_ratio = 0
        if downside_volatility > 0:
            sortino_ratio = np.sqrt(self.annualization_factor) * np.mean(self.daily_returns) / downside_volatility
        metrics['索提诺比率'] = f"{sortino_ratio:.4f}"
        
        # 添加ETF持仓分析指标
        etf_analysis = self.analyze_etf_holdings()
        metrics['ETF1换仓胜率'] = f"{etf_analysis['win_rate']:.2%}"
        metrics['总换仓次数'] = f"{etf_analysis['total_trade_pairs']}"
        metrics['ETF1增仓次数'] = f"{etf_analysis['winning_trades']}"
        metrics['ETF1平均持仓收益'] = f"{etf_analysis['avg_holding_return']:.2%}"
        metrics['ETF1年化持仓增长率'] = f"{etf_analysis['annual_holdings_growth']:.2%}"
        metrics['ETF1持仓波动率'] = f"{etf_analysis['holdings_volatility_ratio']:.4f}"
        
        # 转换为DataFrame并转置
        return pd.DataFrame(metrics, index=['指标值']).T 