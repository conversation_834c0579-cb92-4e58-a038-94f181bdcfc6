import numpy as np
import pandas as pd
from typing import Tuple, Optional, Dict
import warnings

class KalmanFilter:
    """
    卡尔曼滤波器实现，用于ETF配对交易中的价格比率动态建模
    
    状态向量: [price_ratio, price_ratio_velocity]
    观测向量: [price_ratio]
    
    该实现针对配对交易进行了优化，相比传统滚动回归具有以下优势：
    1. 计算复杂度低，适合实时处理
    2. 能够动态调整模型参数
    3. 提供不确定性估计
    """
    
    def __init__(
        self,
        process_noise_ratio: float = 1e-4,
        measurement_noise_ratio: float = 1e-2,
        initial_state_variance: float = 1.0,
        velocity_variance: float = 1e-3
    ):
        """
        初始化卡尔曼滤波器
        
        Args:
            process_noise_ratio: 过程噪声比率，控制模型对新信息的敏感度
            measurement_noise_ratio: 测量噪声比率，反映观测数据的可靠性
            initial_state_variance: 初始状态方差
            velocity_variance: 速度状态的方差
        """
        self.process_noise_ratio = process_noise_ratio
        self.measurement_noise_ratio = measurement_noise_ratio
        self.initial_state_variance = initial_state_variance
        self.velocity_variance = velocity_variance
        
        # 状态向量 [价格比率, 价格比率变化速度]
        self.state = None  # 2x1 向量
        self.covariance = None  # 2x2 协方差矩阵
        
        # 系统矩阵
        self.F = np.array([[1.0, 1.0],    # 状态转移矩阵
                          [0.0, 1.0]])
        
        self.H = np.array([[1.0, 0.0]])   # 观测矩阵 (只观测价格比率)
        
        # 噪声矩阵将在每次更新时动态计算
        self.Q = None  # 过程噪声协方差矩阵
        self.R = None  # 测量噪声协方差矩阵
        
        # 历史记录
        self.history = {
            'states': [],
            'covariances': [],
            'innovations': [],
            'innovation_covariances': [],
            'timestamps': []
        }
        
        self.is_initialized = False
    
    def initialize(self, initial_observation: float) -> None:
        """
        使用第一个观测值初始化滤波器
        
        Args:
            initial_observation: 初始价格比率观测值
        """
        # 初始化状态向量 [价格比率, 变化速度=0]
        self.state = np.array([[initial_observation], [0.0]])
        
        # 初始化协方差矩阵
        self.covariance = np.array([
            [self.initial_state_variance, 0.0],
            [0.0, self.velocity_variance]
        ])
        
        self.is_initialized = True
    
    def _update_noise_matrices(self, observation_value: float) -> None:
        """
        动态更新噪声矩阵，基于当前观测值的大小
        
        Args:
            observation_value: 当前观测值
        """
        # 过程噪声矩阵 - 基于观测值动态调整
        base_process_noise = abs(observation_value) * self.process_noise_ratio
        self.Q = np.array([
            [base_process_noise, 0.0],
            [0.0, base_process_noise * 0.1]  # 速度的过程噪声更小
        ])
        
        # 测量噪声矩阵
        measurement_noise = abs(observation_value) * self.measurement_noise_ratio
        self.R = np.array([[measurement_noise]])
    
    def predict(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测步骤：基于系统模型预测下一个状态
        
        Returns:
            predicted_state: 预测的状态向量
            predicted_covariance: 预测的协方差矩阵
        """
        if not self.is_initialized:
            raise ValueError("滤波器未初始化，请先调用initialize()方法")
        
        # 状态预测: x_k|k-1 = F * x_k-1|k-1
        predicted_state = self.F @ self.state
        
        # 协方差预测: P_k|k-1 = F * P_k-1|k-1 * F^T + Q
        predicted_covariance = self.F @ self.covariance @ self.F.T + self.Q
        
        return predicted_state, predicted_covariance
    
    def update(self, observation: float, timestamp: Optional[pd.Timestamp] = None) -> Dict:
        """
        更新步骤：使用新的观测值更新状态估计
        
        Args:
            observation: 新的价格比率观测值
            timestamp: 时间戳（可选）
            
        Returns:
            包含滤波结果的字典
        """
        if not self.is_initialized:
            self.initialize(observation)
            result = {
                'state': self.state.copy(),
                'covariance': self.covariance.copy(),
                'price_ratio': self.state[0, 0],
                'velocity': self.state[1, 0],
                'uncertainty': np.sqrt(self.covariance[0, 0]),
                'innovation': 0.0,
                'innovation_variance': self.covariance[0, 0]
            }
            self._record_history(result, timestamp)
            return result
        
        # 更新噪声矩阵
        self._update_noise_matrices(observation)
        
        # 预测步骤
        predicted_state, predicted_covariance = self.predict()
        
        # 观测向量
        z = np.array([[observation]])
        
        # 创新(innovation): y = z - H * x_k|k-1
        innovation = z - self.H @ predicted_state
        
        # 创新协方差: S = H * P_k|k-1 * H^T + R
        innovation_covariance = self.H @ predicted_covariance @ self.H.T + self.R
        
        # 卡尔曼增益: K = P_k|k-1 * H^T * S^-1
        try:
            kalman_gain = predicted_covariance @ self.H.T @ np.linalg.inv(innovation_covariance)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            kalman_gain = predicted_covariance @ self.H.T @ np.linalg.pinv(innovation_covariance)
        
        # 状态更新: x_k|k = x_k|k-1 + K * y
        self.state = predicted_state + kalman_gain @ innovation
        
        # 协方差更新: P_k|k = (I - K * H) * P_k|k-1
        I = np.eye(self.state.shape[0])
        self.covariance = (I - kalman_gain @ self.H) @ predicted_covariance
        
        # 构建返回结果
        result = {
            'state': self.state.copy(),
            'covariance': self.covariance.copy(),
            'price_ratio': self.state[0, 0],
            'velocity': self.state[1, 0],
            'uncertainty': np.sqrt(self.covariance[0, 0]),
            'innovation': innovation[0, 0],
            'innovation_variance': innovation_covariance[0, 0],
            'kalman_gain': kalman_gain.copy()
        }
        
        # 记录历史
        self._record_history(result, timestamp)

        return result

    def _record_history(self, result: Dict, timestamp: Optional[pd.Timestamp] = None) -> None:
        """
        记录滤波器历史状态

        Args:
            result: 滤波结果字典
            timestamp: 时间戳
        """
        self.history['states'].append(result['state'].copy())
        self.history['covariances'].append(result['covariance'].copy())
        self.history['innovations'].append(result['innovation'])
        self.history['innovation_covariances'].append(result['innovation_variance'])
        self.history['timestamps'].append(timestamp)

    def get_trading_bounds(self, confidence_level: float = 2.0) -> Tuple[float, float]:
        """
        基于当前状态估计计算交易边界

        Args:
            confidence_level: 置信水平（标准差倍数）

        Returns:
            (lower_bound, upper_bound): 交易边界
        """
        if not self.is_initialized or self.state is None or self.covariance is None:
            raise ValueError("滤波器未初始化")

        current_ratio = self.state[0, 0]
        uncertainty = np.sqrt(self.covariance[0, 0])

        lower_bound = current_ratio - confidence_level * uncertainty
        upper_bound = current_ratio + confidence_level * uncertainty

        return lower_bound, upper_bound

    def get_mean_reversion_signal(self, current_observation: float, threshold: float = 1.0) -> int:
        """
        基于卡尔曼滤波器状态生成均值回归交易信号

        Args:
            current_observation: 当前观测的价格比率
            threshold: 交易阈值（标准差倍数）

        Returns:
            交易信号: 1 (买入ETF1), -1 (卖出ETF1), 0 (无信号)
        """
        if not self.is_initialized or self.state is None or self.covariance is None:
            return 0

        # 获取当前状态估计
        estimated_ratio = self.state[0, 0]
        uncertainty = np.sqrt(self.covariance[0, 0])

        # 避免除零错误
        if uncertainty == 0:
            return 0

        # 计算偏离程度（标准化）
        deviation = (current_observation - estimated_ratio) / uncertainty

        # 生成交易信号
        if deviation > threshold:
            return -1  # 当前比率过高，卖出ETF1买入ETF2
        elif deviation < -threshold:
            return 1   # 当前比率过低，买入ETF1卖出ETF2
        else:
            return 0   # 无交易信号

    def get_state_summary(self) -> Dict:
        """
        获取当前滤波器状态摘要

        Returns:
            状态摘要字典
        """
        if not self.is_initialized or self.state is None or self.covariance is None:
            return {'initialized': False}

        # 计算相关系数，避免除零错误
        correlation = 0.0
        if self.covariance[0, 0] > 0 and self.covariance[1, 1] > 0:
            correlation = self.covariance[0, 1] / (np.sqrt(self.covariance[0, 0]) * np.sqrt(self.covariance[1, 1]))

        return {
            'initialized': True,
            'price_ratio': self.state[0, 0],
            'velocity': self.state[1, 0],
            'uncertainty': np.sqrt(self.covariance[0, 0]),
            'velocity_uncertainty': np.sqrt(self.covariance[1, 1]),
            'correlation': correlation,
            'total_observations': len(self.history['states'])
        }

    def reset(self) -> None:
        """
        重置滤波器状态
        """
        self.state = None
        self.covariance = None
        self.Q = None
        self.R = None
        self.history = {
            'states': [],
            'covariances': [],
            'innovations': [],
            'innovation_covariances': [],
            'timestamps': []
        }
        self.is_initialized = False

    def get_innovation_statistics(self, window: int = 50) -> Dict:
        """
        计算创新序列的统计特性，用于模型诊断

        Args:
            window: 计算统计量的窗口大小

        Returns:
            创新统计量字典
        """
        if len(self.history['innovations']) < 2:
            return {'sufficient_data': False}

        innovations = np.array(self.history['innovations'][-window:])
        innovation_vars = np.array(self.history['innovation_covariances'][-window:])

        # 标准化创新
        standardized_innovations = innovations / np.sqrt(innovation_vars)

        return {
            'sufficient_data': True,
            'mean': np.mean(standardized_innovations),
            'std': np.std(standardized_innovations),
            'skewness': self._calculate_skewness(standardized_innovations),
            'kurtosis': self._calculate_kurtosis(standardized_innovations),
            'ljung_box_stat': self._ljung_box_test(standardized_innovations),
            'window_size': len(innovations)
        }

    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return float(np.mean(((data - mean) / std) ** 3))

    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return float(np.mean(((data - mean) / std) ** 4) - 3)

    def _ljung_box_test(self, data: np.ndarray, lags: int = 10) -> float:
        """简化的Ljung-Box统计量计算"""
        if len(data) < lags + 1:
            return 0.0

        n = len(data)
        autocorrs = []

        for lag in range(1, min(lags + 1, n)):
            if n - lag > 0:
                corr = np.corrcoef(data[:-lag], data[lag:])[0, 1]
                if not np.isnan(corr):
                    autocorrs.append(corr ** 2 / (n - lag))

        if not autocorrs:
            return 0.0

        return n * (n + 2) * sum(autocorrs)
